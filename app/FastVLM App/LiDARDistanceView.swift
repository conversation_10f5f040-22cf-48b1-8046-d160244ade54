//
// For licensing see accompanying LICENSE file.
// Copyright (C) 2025 Apple Inc. All Rights Reserved.
//

import SwiftUI

/// LiDAR距离显示视图
struct LiDARDistanceView: View {
    @State private var isShowingSettings = false

    // 使用@AppStorage直接绑定UserDefaults，确保UI状态同步
    @AppStorage("lidarDistanceSensingEnabled") private var isLiDAREnabled = false
    @AppStorage("lidarHapticFeedbackEnabled") private var isHapticEnabled = false
    @AppStorage("lidarVoiceDistanceEnabled") private var isVoiceEnabled = false
    @AppStorage("lidarCameraControlEnabled") private var isCameraControlEnabled = false

    // 直接使用共享实例获取距离数据
    private var lidarSensor: LiDARDistanceSensor {
        LiDARDistanceSensor.shared
    }

    var body: some View {
        VStack(spacing: 8) {
            // LiDAR距离感知开关
            Toggle(isOn: Binding(
                get: { isLiDAREnabled },
                set: { newValue in
                    print("🔄 LiDAR开关切换: \(isLiDAREnabled) -> \(newValue)")
                    isLiDAREnabled = newValue
                    // 同步到LiDAR传感器
                    lidarSensor.isEnabled = newValue
                }
            )) {
                HStack {
                    Image(systemName: "sensor")
                        .foregroundColor(.accentColor)
                    Text("LiDAR距离感知")
                        .font(.subheadline)
                }
            }
            .toggleStyle(SwitchToggleStyle(tint: .accentColor))

            // 如果启用了LiDAR距离感知，显示当前状态
            if isLiDAREnabled {
                // 距离和威胁等级显示
                HStack {
                    // 距离显示
                    VStack(alignment: .leading, spacing: 2) {
                        Text("距离")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("\(String(format: "%.2f", lidarSensor.currentDistance))米")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    Spacer()

                    // 威胁等级显示
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("威胁等级")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(lidarSensor.threatLevel.description)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(lidarSensor.threatLevel.color)
                    }
                }
                .padding(.horizontal, 4)
                .padding(.top, 2)

                // 距离指示条
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // 背景条
                        Rectangle()
                            .fill(Color.secondary.opacity(0.2))
                            .frame(height: 8)
                            .cornerRadius(4)

                        // 距离指示条
                        Rectangle()
                            .fill(distanceColor)
                            .frame(width: calculateBarWidth(geometry.size.width), height: 8)
                            .cornerRadius(4)
                    }
                }
                .frame(height: 8)
                .padding(.horizontal, 4)
                .padding(.top, 2)

                // 振动反馈开关
                Toggle(isOn: Binding(
                    get: { isHapticEnabled },
                    set: { newValue in
                        print("🔄 振动反馈开关切换: \(isHapticEnabled) -> \(newValue)")
                        isHapticEnabled = newValue
                        lidarSensor.hapticFeedbackEnabled = newValue
                    }
                )) {
                    HStack {
                        Image(systemName: "iphone.radiowaves.left.and.right")
                            .foregroundColor(.accentColor)
                        Text("振动反馈")
                            .font(.subheadline)
                    }
                }
                .toggleStyle(SwitchToggleStyle(tint: .accentColor))
                .padding(.top, 4)

                // 语音距离播报开关
                Toggle(isOn: Binding(
                    get: { isVoiceEnabled },
                    set: { newValue in
                        print("🔄 语音播报开关切换: \(isVoiceEnabled) -> \(newValue)")
                        isVoiceEnabled = newValue
                        lidarSensor.voiceDistanceEnabled = newValue
                    }
                )) {
                    HStack {
                        Image(systemName: "speaker.wave.2.fill")
                            .foregroundColor(.accentColor)
                        Text("语音距离播报")
                            .font(.subheadline)
                    }
                }
                .toggleStyle(SwitchToggleStyle(tint: .accentColor))
                .padding(.top, 4)

                // 摄像头控制开关
                Toggle(isOn: Binding(
                    get: { isCameraControlEnabled },
                    set: { newValue in
                        print("🔄 摄像头控制开关切换: \(isCameraControlEnabled) -> \(newValue)")
                        isCameraControlEnabled = newValue
                        lidarSensor.cameraControlEnabled = newValue
                    }
                )) {
                    HStack {
                        Image(systemName: "camera.fill")
                            .foregroundColor(.accentColor)
                        VStack(alignment: .leading, spacing: 2) {
                            Text("摄像头控制")
                                .font(.subheadline)
                            Text("启用时LiDAR会自动关闭摄像头")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .toggleStyle(SwitchToggleStyle(tint: .accentColor))
                .padding(.top, 4)

                // 设置按钮
                Button {
                    isShowingSettings.toggle()
                } label: {
                    HStack {
                        Image(systemName: "slider.horizontal.3")
                            .foregroundColor(.accentColor)
                        Text("距离感知设置")
                            .font(.subheadline)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                .buttonStyle(.borderless)
                .padding(.top, 4)
            }
        }
        .padding(.horizontal, 4)
        .animation(.easeInOut(duration: 0.2), value: lidarSensor.isEnabled)
        .onAppear {
            // 确保UI状态与UserDefaults同步
            print("📱 LiDAR视图出现，当前状态检查：")
            print("  - LiDAR主开关: \(isLiDAREnabled)")
            print("  - 振动反馈: \(isHapticEnabled)")
            print("  - 语音播报: \(isVoiceEnabled)")
            print("  - 摄像头控制: \(isCameraControlEnabled)")

            // 确保LiDAR传感器状态与UI同步
            lidarSensor.isEnabled = isLiDAREnabled
            lidarSensor.hapticFeedbackEnabled = isHapticEnabled
            lidarSensor.voiceDistanceEnabled = isVoiceEnabled
            lidarSensor.cameraControlEnabled = isCameraControlEnabled

            // 如果所有功能都关闭，确保停止触觉反馈
            if !isLiDAREnabled || !isHapticEnabled {
                HapticFeedbackManager.shared.stopHapticFeedback()
                print("🛑 应用启动时停止触觉反馈")
            }
        }
        .sheet(isPresented: $isShowingSettings) {
            LiDARSettingsView()
        }
    }

    /// 计算距离指示条的宽度
    private func calculateBarWidth(_ totalWidth: CGFloat) -> CGFloat {
        // 如果距离为0或大于低威胁阈值，返回0或最大宽度
        if lidarSensor.currentDistance <= 0 {
            return 0
        } else if lidarSensor.currentDistance >= lidarSensor.lowThreatThreshold {
            return totalWidth
        }

        // 计算距离占最大阈值的比例
        let ratio = CGFloat(lidarSensor.currentDistance / lidarSensor.lowThreatThreshold)
        return totalWidth * ratio
    }

    /// 根据距离获取颜色
    private var distanceColor: Color {
        switch lidarSensor.threatLevel {
        case .high:
            return .red
        case .medium:
            return .orange
        case .low:
            return .yellow
        case .none:
            return .green
        }
    }
}

#Preview {
    LiDARDistanceView()
        .padding()
        .previewLayout(.sizeThatFits)
}
