//
// For licensing see accompanying LICENSE file.
// Copyright (C) 2025 Apple Inc. All Rights Reserved.
//

import SwiftUI

/// LiDAR距离显示视图
struct LiDARDistanceView: View {
    @State private var isShowingSettings = false
    @State private var refreshTrigger = false

    // 直接使用共享实例，而不是创建状态副本
    private var lidarSensor: LiDARDistanceSensor {
        LiDARDistanceSensor.shared
    }

    var body: some View {
        VStack(spacing: 8) {
            // LiDAR距离感知开关
            Toggle(isOn: Binding(
                get: {
                    let currentState = lidarSensor.isEnabled
                    // 触发UI刷新以确保状态同步
                    _ = refreshTrigger
                    return currentState
                },
                set: { newValue in
                    print("🔄 LiDAR开关切换: \(lidarSensor.isEnabled) -> \(newValue)")
                    lidarSensor.isEnabled = newValue
                    // 强制UI刷新
                    refreshTrigger.toggle()
                }
            )) {
                HStack {
                    Image(systemName: "sensor")
                        .foregroundColor(.accentColor)
                    Text("LiDAR距离感知")
                        .font(.subheadline)
                }
            }
            .toggleStyle(SwitchToggleStyle(tint: .accentColor))

            // 如果启用了LiDAR距离感知，显示当前状态
            if lidarSensor.isEnabled {
                // 距离和威胁等级显示
                HStack {
                    // 距离显示
                    VStack(alignment: .leading, spacing: 2) {
                        Text("距离")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("\(String(format: "%.2f", lidarSensor.currentDistance))米")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    Spacer()

                    // 威胁等级显示
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("威胁等级")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(lidarSensor.threatLevel.description)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(lidarSensor.threatLevel.color)
                    }
                }
                .padding(.horizontal, 4)
                .padding(.top, 2)

                // 距离指示条
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // 背景条
                        Rectangle()
                            .fill(Color.secondary.opacity(0.2))
                            .frame(height: 8)
                            .cornerRadius(4)

                        // 距离指示条
                        Rectangle()
                            .fill(distanceColor)
                            .frame(width: calculateBarWidth(geometry.size.width), height: 8)
                            .cornerRadius(4)
                    }
                }
                .frame(height: 8)
                .padding(.horizontal, 4)
                .padding(.top, 2)

                // 振动反馈开关
                Toggle(isOn: Binding(
                    get: { lidarSensor.hapticFeedbackEnabled },
                    set: { newValue in
                        print("🔄 振动反馈开关切换: \(lidarSensor.hapticFeedbackEnabled) -> \(newValue)")
                        lidarSensor.hapticFeedbackEnabled = newValue
                    }
                )) {
                    HStack {
                        Image(systemName: "iphone.radiowaves.left.and.right")
                            .foregroundColor(.accentColor)
                        Text("振动反馈")
                            .font(.subheadline)
                    }
                }
                .toggleStyle(SwitchToggleStyle(tint: .accentColor))
                .padding(.top, 4)

                // 语音距离播报开关
                Toggle(isOn: Binding(
                    get: { lidarSensor.voiceDistanceEnabled },
                    set: { newValue in
                        print("🔄 语音播报开关切换: \(lidarSensor.voiceDistanceEnabled) -> \(newValue)")
                        lidarSensor.voiceDistanceEnabled = newValue
                    }
                )) {
                    HStack {
                        Image(systemName: "speaker.wave.2.fill")
                            .foregroundColor(.accentColor)
                        Text("语音距离播报")
                            .font(.subheadline)
                    }
                }
                .toggleStyle(SwitchToggleStyle(tint: .accentColor))
                .padding(.top, 4)

                // 摄像头控制开关
                Toggle(isOn: Binding(
                    get: { lidarSensor.cameraControlEnabled },
                    set: { newValue in
                        print("🔄 摄像头控制开关切换: \(lidarSensor.cameraControlEnabled) -> \(newValue)")
                        lidarSensor.cameraControlEnabled = newValue
                    }
                )) {
                    HStack {
                        Image(systemName: "camera.fill")
                            .foregroundColor(.accentColor)
                        VStack(alignment: .leading, spacing: 2) {
                            Text("摄像头控制")
                                .font(.subheadline)
                            Text("启用时LiDAR会自动关闭摄像头")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .toggleStyle(SwitchToggleStyle(tint: .accentColor))
                .padding(.top, 4)

                // 设置按钮
                Button {
                    isShowingSettings.toggle()
                } label: {
                    HStack {
                        Image(systemName: "slider.horizontal.3")
                            .foregroundColor(.accentColor)
                        Text("距离感知设置")
                            .font(.subheadline)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                .buttonStyle(.borderless)
                .padding(.top, 4)
            }
        }
        .padding(.horizontal, 4)
        .animation(.easeInOut(duration: 0.2), value: lidarSensor.isEnabled)
        .onAppear {
            // 确保UI状态与UserDefaults同步
            print("📱 LiDAR视图出现，当前状态检查：")
            print("  - LiDAR主开关: \(lidarSensor.isEnabled)")
            print("  - 振动反馈: \(lidarSensor.hapticFeedbackEnabled)")
            print("  - 语音播报: \(lidarSensor.voiceDistanceEnabled)")
            print("  - 摄像头控制: \(lidarSensor.cameraControlEnabled)")

            // 强制UI刷新以确保显示正确状态
            refreshTrigger.toggle()
        }
        .sheet(isPresented: $isShowingSettings) {
            LiDARSettingsView()
        }
    }

    /// 计算距离指示条的宽度
    private func calculateBarWidth(_ totalWidth: CGFloat) -> CGFloat {
        // 如果距离为0或大于低威胁阈值，返回0或最大宽度
        if lidarSensor.currentDistance <= 0 {
            return 0
        } else if lidarSensor.currentDistance >= lidarSensor.lowThreatThreshold {
            return totalWidth
        }

        // 计算距离占最大阈值的比例
        let ratio = CGFloat(lidarSensor.currentDistance / lidarSensor.lowThreatThreshold)
        return totalWidth * ratio
    }

    /// 根据距离获取颜色
    private var distanceColor: Color {
        switch lidarSensor.threatLevel {
        case .high:
            return .red
        case .medium:
            return .orange
        case .low:
            return .yellow
        case .none:
            return .green
        }
    }
}

#Preview {
    LiDARDistanceView()
        .padding()
        .previewLayout(.sizeThatFits)
}
