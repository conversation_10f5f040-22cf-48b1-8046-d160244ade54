//
// For licensing see accompanying LICENSE file.
// Copyright (C) 2025 Apple Inc. All Rights Reserved.
//

import Foundation
import SwiftUI

/// 触觉反馈参数可视化工具
struct HapticFeedbackVisualization {

    /// 生成距离-触觉参数映射表
    static func generateParameterMapping() -> [(distance: Float, interval: Double, intensity: Float, sharpness: Float)] {
        var mapping: [(distance: Float, interval: Double, intensity: Float, sharpness: Float)] = []

        // 生成从0.1米到5.0米的参数映射
        let distances: [Float] = [0.1, 0.2, 0.3, 0.5, 0.7, 1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0]

        for distance in distances {
            let interval = calculatePreciseInterval(for: distance)
            let intensity = calculatePreciseIntensity(for: distance)
            let sharpness = calculatePreciseSharpness(for: distance)

            mapping.append((distance: distance, interval: interval, intensity: intensity, sharpness: sharpness))
        }

        return mapping
    }

    /// 打印触觉参数映射表
    static func printParameterMapping() {
        print("\n📊 精确触觉反馈参数映射表")
        print("=" * 80)
        print("距离(米) | 间隔(秒) | 频率(Hz) | 强度(%) | 锐度(%) | 描述")
        print("-" * 80)

        let mapping = generateParameterMapping()

        for item in mapping {
            let frequency = 1.0 / item.interval
            let intensityPercent = Int(item.intensity * 100)
            let sharpnessPercent = Int(item.sharpness * 100)

            let description = getDistanceDescription(for: item.distance)

            print(String(format: "%6.1f   | %7.3f  | %6.1f  | %5d%% | %5d%% | %@",
                        item.distance, item.interval, frequency, intensityPercent, sharpnessPercent, description))
        }

        print("=" * 80)
        print("说明：")
        print("- 间隔：两次振动之间的时间间隔")
        print("- 频率：每秒振动次数")
        print("- 强度：振动的力度大小")
        print("- 锐度：振动的清晰度和尖锐感")
        print("- 距离越近，频率越高，强度和锐度越大")
    }

    /// 计算精确间隔（复制HapticFeedbackManager的逻辑）
    static func calculatePreciseInterval(for distance: Float) -> Double {
        let clampedDistance = max(0.1, min(distance, 5.0))
        let normalizedDistance = Double(clampedDistance) / 5.0
        let exponentialFactor = pow(normalizedDistance, 1.5)
        let interval = 0.05 + 1.45 * exponentialFactor
        return interval
    }

    /// 计算精确强度（复制HapticFeedbackManager的逻辑）
    static func calculatePreciseIntensity(for distance: Float) -> Float {
        let clampedDistance = max(0.1, min(distance, 5.0))
        let normalizedDistance = clampedDistance / 5.0
        let intensityFactor = pow(1.0 - normalizedDistance, 1.8)
        let intensity = 0.3 + 0.7 * intensityFactor
        return Float(intensity)
    }

    /// 计算精确锐度（复制HapticFeedbackManager的逻辑）
    static func calculatePreciseSharpness(for distance: Float) -> Float {
        let clampedDistance = max(0.1, min(distance, 5.0))
        let normalizedDistance = clampedDistance / 5.0
        let sharpnessFactor = pow(1.0 - normalizedDistance, 1.5)
        let sharpness = 0.2 + 0.6 * sharpnessFactor
        return Float(sharpness)
    }

    /// 获取距离描述
    static func getDistanceDescription(for distance: Float) -> String {
        switch distance {
        case 0.0..<0.3:
            return "极近距离 - 紧急警告"
        case 0.3..<0.6:
            return "很近距离 - 高度警戒"
        case 0.6..<1.0:
            return "近距离 - 注意避让"
        case 1.0..<2.0:
            return "中等距离 - 保持警觉"
        case 2.0..<3.0:
            return "较远距离 - 轻度提醒"
        case 3.0..<4.0:
            return "远距离 - 低频提醒"
        default:
            return "很远距离 - 偶尔提醒"
        }
    }
}

/// SwiftUI视图：触觉反馈参数可视化
struct HapticParameterVisualizationView: View {
    @State private var selectedDistance: Float = 1.0

    var body: some View {
        VStack(spacing: 20) {
            Text("触觉反馈参数可视化")
                .font(.title2)
                .fontWeight(.bold)

            VStack(alignment: .leading, spacing: 10) {
                Text("距离: \(String(format: "%.1f", selectedDistance))米")
                    .font(.headline)

                Slider(value: $selectedDistance, in: 0.1...5.0, step: 0.1)
                    .accentColor(.blue)

                let interval = HapticFeedbackVisualization.calculatePreciseInterval(for: selectedDistance)
                let intensity = HapticFeedbackVisualization.calculatePreciseIntensity(for: selectedDistance)
                let sharpness = HapticFeedbackVisualization.calculatePreciseSharpness(for: selectedDistance)
                let frequency = 1.0 / interval

                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("振动间隔:")
                        Spacer()
                        Text("\(String(format: "%.3f", interval))秒")
                            .fontWeight(.medium)
                    }

                    HStack {
                        Text("振动频率:")
                        Spacer()
                        Text("\(String(format: "%.1f", frequency))Hz")
                            .fontWeight(.medium)
                    }

                    HStack {
                        Text("振动强度:")
                        Spacer()
                        Text("\(Int(intensity * 100))%")
                            .fontWeight(.medium)
                    }

                    HStack {
                        Text("振动锐度:")
                        Spacer()
                        Text("\(Int(sharpness * 100))%")
                            .fontWeight(.medium)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)

                Text(HapticFeedbackVisualization.getDistanceDescription(for: selectedDistance))
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .frame(maxWidth: .infinity)
            }
            .padding()

            Button("测试当前距离的触觉反馈") {
                HapticFeedbackManager.shared.playDynamicHapticFeedback(forDistance: selectedDistance)

                // 3秒后停止
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    HapticFeedbackManager.shared.stopHapticFeedback()
                }
            }
            .buttonStyle(.borderedProminent)

            Button("打印完整参数映射表") {
                HapticFeedbackVisualization.printParameterMapping()
            }
            .buttonStyle(.bordered)
        }
        .padding()
    }
}

// 扩展String以支持重复操作符
extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}
