//
// For licensing see accompanying LICENSE file.
// Copyright (C) 2025 Apple Inc. All Rights Reserved.
//

import Foundation
import CoreHaptics
import UIKit

/// 极致精细的振动反馈管理器，使用最先进的CoreHaptics API
/// 针对iPhone 14 Pro Max及更高规格设备优化，提供令人惊艳的触觉体验
class HapticFeedbackManager {

    /// 单例实例
    static let shared = HapticFeedbackManager()

    /// 触觉引擎
    private var engine: CHHapticEngine?

    /// 高级触觉播放器
    private var advancedPlayer: CHHapticAdvancedPatternPlayer?

    /// 当前是否正在播放触觉反馈
    private var isPlaying = false

    /// 当前的距离值（米）
    private var currentDistance: Float = 0

    /// 上次更新距离的时间
    private var lastUpdateTime: CFTimeInterval = 0

    /// 距离变化速度（米/秒）
    private var distanceVelocity: Float = 0

    /// 上次的距离值
    private var previousDistance: Float = 0

    /// 触觉反馈模式
    private enum HapticMode {
        case none
        case ultraPrecise // 超精细模式
    }

    /// 当前触觉模式
    private var currentMode: HapticMode = .none

    /// 动态参数控制器
    private var intensityController: CHHapticParameterCurve?
    private var sharpnessController: CHHapticParameterCurve?
    private var frequencyController: CHHapticParameterCurve?

    /// 触觉事件队列
    private var hapticEventQueue: DispatchQueue

    /// 距离感知精度等级
    private enum PrecisionLevel {
        case ultraFine    // 0-0.5米：超精细感知
        case fine         // 0.5-1.5米：精细感知
        case standard     // 1.5-3.0米：标准感知
        case coarse       // 3.0-5.0米：粗糙感知
    }

    /// 初始化
    private init() {
        // 创建专用的触觉事件处理队列
        hapticEventQueue = DispatchQueue(label: "com.fastvlm.haptic", qos: .userInteractive)
        setupAdvancedHapticEngine()
    }

    /// 设置高级触觉引擎
    private func setupAdvancedHapticEngine() {
        // 检查设备是否支持高级触觉功能
        let capabilities = CHHapticEngine.capabilitiesForHardware()
        guard capabilities.supportsHaptics else {
            print("设备不支持触觉引擎")
            return
        }

        do {
            // 创建高性能触觉引擎
            engine = try CHHapticEngine()

            // 设置引擎为高性能模式（针对iPhone 14 Pro Max优化）
            if #available(iOS 16.0, *) {
                engine?.playsHapticsOnly = true // 仅播放触觉，不播放音频
            }

            // 设置引擎重置处理程序
            engine?.resetHandler = { [weak self] in
                print("🔄 高级触觉引擎重置")
                self?.hapticEventQueue.async {
                    do {
                        try self?.engine?.start()
                        self?.recreateAdvancedPlayer()
                    } catch {
                        print("❌ 触觉引擎重启失败: \(error)")
                    }
                }
            }

            // 设置引擎停止处理程序
            engine?.stoppedHandler = { reason in
                print("⏹️ 触觉引擎停止，原因: \(reason)")
            }

            // 启动引擎
            try engine?.start()
            print("✅ 高级触觉引擎启动成功")

        } catch {
            print("❌ 高级触觉引擎创建失败: \(error)")
        }
    }

    /// 重新创建高级播放器
    private func recreateAdvancedPlayer() {
        do {
            try advancedPlayer?.stop(atTime: CHHapticTimeImmediate)
        } catch {
            print("❌ 停止高级播放器失败: \(error)")
        }
        advancedPlayer = nil
        // 播放器将在下次需要时重新创建
    }

    /// 播放轻微触觉反馈（兼容性方法）
    func playLightHapticFeedback() {
        playDynamicHapticFeedback(forDistance: 4.0)
    }

    /// 播放中等触觉反馈（兼容性方法）
    func playMediumHapticFeedback() {
        playDynamicHapticFeedback(forDistance: 2.0)
    }

    /// 播放强烈触觉反馈（兼容性方法）
    func playStrongHapticFeedback() {
        playDynamicHapticFeedback(forDistance: 0.5)
    }

    /// 🚀 播放超精细的基于距离的动态触觉反馈
    /// 使用最先进的CoreHaptics API，提供令人惊艳的触觉体验
    func playDynamicHapticFeedback(forDistance distance: Float) {
        hapticEventQueue.async { [weak self] in
            // 确保引擎处于可用状态
            self?.ensureEngineIsReady()
            self?.updateDistanceAndVelocity(distance)
            self?.createUltraPreciseHapticExperience()
        }
    }

    /// 确保触觉引擎处于就绪状态
    private func ensureEngineIsReady() {
        // 检查引擎是否存在且正在运行
        guard let engine = engine else {
            print("⚠️ 触觉引擎不存在，重新创建")
            setupAdvancedHapticEngine()
            return
        }

        // 检查引擎状态
        if engine.currentTime == 0 {
            print("⚠️ 触觉引擎未启动，尝试启动")
            do {
                try engine.start()
                print("✅ 触觉引擎重新启动成功")
            } catch {
                print("❌ 触觉引擎启动失败: \(error)")
                // 重新创建引擎
                setupAdvancedHapticEngine()
            }
        }
    }

    /// 更新距离和计算速度
    private func updateDistanceAndVelocity(_ newDistance: Float) {
        let currentTime = CACurrentMediaTime()

        if lastUpdateTime > 0 {
            let deltaTime = Float(currentTime - lastUpdateTime)
            if deltaTime > 0 {
                distanceVelocity = (newDistance - currentDistance) / deltaTime
            }
        }

        previousDistance = currentDistance
        currentDistance = newDistance
        lastUpdateTime = currentTime
    }

    /// 创建超精细触觉体验
    private func createUltraPreciseHapticExperience() {
        // 如果距离超出感知范围，停止反馈
        guard currentDistance > 0 && currentDistance <= 5.0 else {
            print("🛑 距离超出范围(\(currentDistance)米)，停止触觉反馈")
            stopHapticFeedback()
            return
        }

        print("🎯 创建触觉体验 - 距离: \(currentDistance)米, 当前模式: \(currentMode)")

        // 如果已经在播放超精细模式，更新参数
        if currentMode == .ultraPrecise {
            print("🔄 更新现有触觉参数")
            updateDynamicParameters()
            return
        }

        // 启动超精细模式
        print("🚀 启动新的超精细触觉模式")
        startUltraPreciseMode()
    }

    /// 启动超精细模式
    private func startUltraPreciseMode() {
        do {
            try engine?.start()

            // 创建超精细触觉模式
            let pattern = createUltraPreciseHapticPattern()

            // 创建高级播放器
            advancedPlayer = try engine?.makeAdvancedPlayer(with: pattern)

            // 启动播放
            try advancedPlayer?.start(atTime: CHHapticTimeImmediate)

            currentMode = .ultraPrecise
            isPlaying = true

            print("🎯 超精细触觉模式已启动 - 距离: \(currentDistance)米")

        } catch {
            print("❌ 超精细触觉模式启动失败: \(error)")
        }
    }

    /// 🎨 创建超精细触觉模式
    private func createUltraPreciseHapticPattern() -> CHHapticPattern {
        var events: [CHHapticEvent] = []

        // 获取精度等级
        let precisionLevel = getPrecisionLevel(for: currentDistance)

        // 根据精度等级创建不同的触觉体验
        switch precisionLevel {
        case .ultraFine:
            events.append(contentsOf: createUltraFineHapticEvents())
        case .fine:
            events.append(contentsOf: createFineHapticEvents())
        case .standard:
            events.append(contentsOf: createStandardHapticEvents())
        case .coarse:
            events.append(contentsOf: createCoarseHapticEvents())
        }

        do {
            // 创建模式时不使用参数，而是通过动态参数控制
            return try CHHapticPattern(events: events, parameters: [])
        } catch {
            print("❌ 创建超精细触觉模式失败: \(error)")
            // 返回简单的备用模式
            return createFallbackPattern()
        }
    }

    /// 获取距离对应的精度等级
    private func getPrecisionLevel(for distance: Float) -> PrecisionLevel {
        switch distance {
        case 0...0.5:
            return .ultraFine
        case 0.5...1.5:
            return .fine
        case 1.5...3.0:
            return .standard
        default:
            return .coarse
        }
    }

    /// 🔥 创建超精细触觉事件（0-0.5米）- 最高精度
    private func createUltraFineHapticEvents() -> [CHHapticEvent] {
        var events: [CHHapticEvent] = []

        // 计算基础参数
        let baseIntensity = calculateUltraFineIntensity()
        let baseSharpness = calculateUltraFineSharpness()

        // 创建"心跳"式的复杂触觉体验
        for i in 0..<20 {
            let time = Double(i) * 0.025 // 每25ms一个脉冲，极高频率

            // 使用正弦波调制强度，创造波动感
            let intensityModulation = 0.8 + 0.2 * sin(Double(i) * 0.8)
            let sharpnessModulation = 0.9 + 0.1 * cos(Double(i) * 0.6)

            let intensity = baseIntensity * Float(intensityModulation)
            let sharpness = baseSharpness * Float(sharpnessModulation)

            // 主脉冲 - 尖锐的瞬时触觉
            let mainEvent = CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: intensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: sharpness)
                ],
                relativeTime: time
            )
            events.append(mainEvent)

            // 微妙的连续震动 - 增加层次感
            if i % 3 == 0 {
                let continuousEvent = CHHapticEvent(
                    eventType: .hapticContinuous,
                    parameters: [
                        CHHapticEventParameter(parameterID: .hapticIntensity, value: intensity * 0.4),
                        CHHapticEventParameter(parameterID: .hapticSharpness, value: sharpness * 0.6)
                    ],
                    relativeTime: time,
                    duration: 0.02
                )
                events.append(continuousEvent)
            }
        }

        return events
    }

    /// ⚡ 创建精细触觉事件（0.5-1.5米）
    private func createFineHapticEvents() -> [CHHapticEvent] {
        var events: [CHHapticEvent] = []

        let baseIntensity = calculateFineIntensity()
        let baseSharpness = calculateFineSharpness()

        // 创建节奏感强的触觉体验
        for i in 0..<12 {
            let time = Double(i) * 0.04 // 每40ms一个脉冲

            let intensity = baseIntensity * Float(0.7 + 0.3 * sin(Double(i) * 0.5))
            let sharpness = baseSharpness * Float(0.8 + 0.2 * cos(Double(i) * 0.4))

            let event = CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: intensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: sharpness)
                ],
                relativeTime: time
            )
            events.append(event)
        }

        return events
    }

    /// 📊 创建标准触觉事件（1.5-3.0米）
    private func createStandardHapticEvents() -> [CHHapticEvent] {
        var events: [CHHapticEvent] = []

        let baseIntensity = calculateStandardIntensity()
        let baseSharpness = calculateStandardSharpness()

        // 创建稳定的触觉体验
        for i in 0..<8 {
            let time = Double(i) * 0.06 // 每60ms一个脉冲

            let intensity = baseIntensity * Float(0.8 + 0.2 * sin(Double(i) * 0.3))
            let sharpness = baseSharpness

            let event = CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: intensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: sharpness)
                ],
                relativeTime: time
            )
            events.append(event)
        }

        return events
    }

    /// 🌊 创建粗糙触觉事件（3.0-5.0米）
    private func createCoarseHapticEvents() -> [CHHapticEvent] {
        var events: [CHHapticEvent] = []

        let baseIntensity = calculateCoarseIntensity()
        let baseSharpness = calculateCoarseSharpness()

        // 创建简单的触觉体验
        for i in 0..<5 {
            let time = Double(i) * 0.1 // 每100ms一个脉冲

            let event = CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: baseIntensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: baseSharpness)
                ],
                relativeTime: time
            )
            events.append(event)
        }

        return events
    }

    /// 停止触觉反馈
    func stopHapticFeedback() {
        hapticEventQueue.async { [weak self] in
            do {
                try self?.advancedPlayer?.stop(atTime: CHHapticTimeImmediate)
            } catch {
                print("❌ 停止触觉反馈失败: \(error)")
            }
            self?.advancedPlayer = nil
            self?.currentMode = .none
            self?.isPlaying = false
            print("🛑 超精细触觉反馈已停止")
        }
    }

    /// 🔧 更新动态参数
    private func updateDynamicParameters() {
        guard let player = advancedPlayer else { return }

        hapticEventQueue.async { [weak self] in
            guard let self = self else { return }

            do {
                // 计算新的强度和锐度
                let newIntensity = self.calculateDynamicIntensity()
                let newSharpness = self.calculateDynamicSharpness()

                // 更新播放器参数
                try player.sendParameters([
                    CHHapticDynamicParameter(parameterID: .hapticIntensityControl, value: newIntensity, relativeTime: 0),
                    CHHapticDynamicParameter(parameterID: .hapticSharpnessControl, value: newSharpness, relativeTime: 0)
                ], atTime: CHHapticTimeImmediate)

                print("🔄 动态参数已更新 - 距离: \(self.currentDistance)米, 强度: \(newIntensity), 锐度: \(newSharpness)")

            } catch {
                print("❌ 更新动态参数失败: \(error)")
            }
        }
    }

    /// 🧮 计算动态强度
    private func calculateDynamicIntensity() -> Float {
        // 使用复杂的非线性算法，考虑距离和速度
        let distanceComponent = pow(max(0.1, 5.0 - currentDistance) / 5.0, 1.8)
        let velocityComponent = min(abs(distanceVelocity) * 0.2, 0.3) // 速度影响

        return min(0.3 + distanceComponent * 0.7 + velocityComponent, 1.0)
    }

    /// 🧮 计算动态锐度
    private func calculateDynamicSharpness() -> Float {
        let distanceComponent = pow(max(0.1, 5.0 - currentDistance) / 5.0, 1.5)
        let velocityComponent = min(abs(distanceVelocity) * 0.15, 0.25)

        return min(0.2 + distanceComponent * 0.6 + velocityComponent, 1.0)
    }

    /// 🎯 计算超精细强度
    private func calculateUltraFineIntensity() -> Float {
        let normalizedDistance = max(0, min(currentDistance, 0.5)) / 0.5
        return 0.8 + (1.0 - normalizedDistance) * 0.2 // 0.8-1.0
    }

    /// 🎯 计算超精细锐度
    private func calculateUltraFineSharpness() -> Float {
        let normalizedDistance = max(0, min(currentDistance, 0.5)) / 0.5
        return 0.7 + (1.0 - normalizedDistance) * 0.3 // 0.7-1.0
    }

    /// ⚡ 计算精细强度
    private func calculateFineIntensity() -> Float {
        let normalizedDistance = max(0, min(currentDistance - 0.5, 1.0)) / 1.0
        return 0.6 + (1.0 - normalizedDistance) * 0.2 // 0.6-0.8
    }

    /// ⚡ 计算精细锐度
    private func calculateFineSharpness() -> Float {
        let normalizedDistance = max(0, min(currentDistance - 0.5, 1.0)) / 1.0
        return 0.5 + (1.0 - normalizedDistance) * 0.2 // 0.5-0.7
    }

    /// 📊 计算标准强度
    private func calculateStandardIntensity() -> Float {
        let normalizedDistance = max(0, min(currentDistance - 1.5, 1.5)) / 1.5
        return 0.4 + (1.0 - normalizedDistance) * 0.2 // 0.4-0.6
    }

    /// 📊 计算标准锐度
    private func calculateStandardSharpness() -> Float {
        let normalizedDistance = max(0, min(currentDistance - 1.5, 1.5)) / 1.5
        return 0.3 + (1.0 - normalizedDistance) * 0.2 // 0.3-0.5
    }

    /// 🌊 计算粗糙强度
    private func calculateCoarseIntensity() -> Float {
        let normalizedDistance = max(0, min(currentDistance - 3.0, 2.0)) / 2.0
        return 0.3 + (1.0 - normalizedDistance) * 0.1 // 0.3-0.4
    }

    /// 🌊 计算粗糙锐度
    private func calculateCoarseSharpness() -> Float {
        return 0.3 // 固定值
    }



    /// 🛡️ 创建备用模式
    private func createFallbackPattern() -> CHHapticPattern {
        let event = CHHapticEvent(
            eventType: .hapticTransient,
            parameters: [
                CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.5),
                CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.5)
            ],
            relativeTime: 0
        )

        do {
            return try CHHapticPattern(events: [event], parameters: [])
        } catch {
            fatalError("无法创建备用触觉模式: \(error)")
        }
    }

    /// 🔄 更新距离值（兼容性方法）
    func updateDistance(_ distance: Float) {
        // 直接调用动态触觉反馈方法
        playDynamicHapticFeedback(forDistance: distance)
    }

    /// 🎮 播放简单的触觉反馈（用于按钮点击等）
    func playSimpleHapticFeedback() {
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.prepare()
        generator.impactOccurred()
        print("🎮 简单触觉反馈已触发")
    }

    /// 🧪 测试触觉反馈功能
    func testHapticFeedback() {
        print("🧪 开始测试触觉反馈功能")

        // 检查设备支持
        let capabilities = CHHapticEngine.capabilitiesForHardware()
        print("  - 设备支持触觉: \(capabilities.supportsHaptics)")
        print("  - 设备支持音频: \(capabilities.supportsAudio)")

        // 检查引擎状态
        if let engine = engine {
            print("  - 引擎存在: ✅")
            print("  - 引擎当前时间: \(engine.currentTime)")
        } else {
            print("  - 引擎存在: ❌")
        }

        // 测试简单反馈
        playSimpleHapticFeedback()

        // 测试动态反馈
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.playDynamicHapticFeedback(forDistance: 1.0)
            print("🧪 动态触觉反馈测试完成")
        }
    }
}
