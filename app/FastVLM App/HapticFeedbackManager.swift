//
// For licensing see accompanying LICENSE file.
// Copyright (C) 2025 Apple Inc. All Rights Reserved.
//

import Foundation
import CoreHaptics
import UIKit

/// 极致精细的振动反馈管理器，使用最先进的CoreHaptics API
/// 针对iPhone 14 Pro Max及更高规格设备优化，提供令人惊艳的触觉体验
class HapticFeedbackManager {

    /// 单例实例
    static let shared = HapticFeedbackManager()

    /// 触觉引擎
    private var engine: CHHapticEngine?

    /// 高级触觉播放器
    private var advancedPlayer: CHHapticAdvancedPatternPlayer?

    /// 当前是否正在播放触觉反馈
    private var isPlaying = false

    /// 当前的距离值（米）
    private var currentDistance: Float = 0

    /// 上次更新距离的时间
    private var lastUpdateTime: CFTimeInterval = 0

    /// 距离变化速度（米/秒）
    private var distanceVelocity: Float = 0

    /// 上次的距离值
    private var previousDistance: Float = 0

    /// 触觉反馈模式
    private enum HapticMode {
        case none
        case ultraPrecise // 超精细模式
    }

    /// 当前触觉模式
    private var currentMode: HapticMode = .none

    /// 动态参数控制器
    private var intensityController: CHHapticParameterCurve?
    private var sharpnessController: CHHapticParameterCurve?
    private var frequencyController: CHHapticParameterCurve?

    /// 触觉事件队列
    private var hapticEventQueue: DispatchQueue

    /// 持续振动定时器
    private var continuousHapticTimer: Timer?

    /// 是否应该持续振动
    private var shouldContinueHaptic = false

    /// 上次更新时间（用于控制日志频率）
    private var lastUpdateTime: CFTimeInterval = 0

    /// 距离感知精度等级
    private enum PrecisionLevel {
        case ultraFine    // 0-0.5米：超精细感知
        case fine         // 0.5-1.5米：精细感知
        case standard     // 1.5-3.0米：标准感知
        case coarse       // 3.0-5.0米：粗糙感知
    }

    /// 初始化
    private init() {
        // 创建专用的触觉事件处理队列
        hapticEventQueue = DispatchQueue(label: "com.fastvlm.haptic", qos: .userInteractive)
        setupAdvancedHapticEngine()
    }

    /// 设置高级触觉引擎
    private func setupAdvancedHapticEngine() {
        // 检查设备是否支持高级触觉功能
        let capabilities = CHHapticEngine.capabilitiesForHardware()
        guard capabilities.supportsHaptics else {
            print("设备不支持触觉引擎")
            return
        }

        do {
            // 创建高性能触觉引擎
            engine = try CHHapticEngine()

            // 设置引擎为高性能模式（针对iPhone 14 Pro Max优化）
            if #available(iOS 16.0, *) {
                engine?.playsHapticsOnly = true // 仅播放触觉，不播放音频
            }

            // 设置引擎重置处理程序
            engine?.resetHandler = { [weak self] in
                print("🔄 高级触觉引擎重置")
                self?.hapticEventQueue.async {
                    do {
                        try self?.engine?.start()
                        self?.recreateAdvancedPlayer()
                    } catch {
                        print("❌ 触觉引擎重启失败: \(error)")
                    }
                }
            }

            // 设置引擎停止处理程序
            engine?.stoppedHandler = { reason in
                print("⏹️ 触觉引擎停止，原因: \(reason)")
            }

            // 启动引擎
            try engine?.start()
            print("✅ 高级触觉引擎启动成功")

        } catch {
            print("❌ 高级触觉引擎创建失败: \(error)")
        }
    }

    /// 重新创建高级播放器
    private func recreateAdvancedPlayer() {
        do {
            try advancedPlayer?.stop(atTime: CHHapticTimeImmediate)
        } catch {
            print("❌ 停止高级播放器失败: \(error)")
        }
        advancedPlayer = nil
        // 播放器将在下次需要时重新创建
    }

    /// 播放轻微触觉反馈（兼容性方法）
    func playLightHapticFeedback() {
        playDynamicHapticFeedback(forDistance: 4.0)
    }

    /// 播放中等触觉反馈（兼容性方法）
    func playMediumHapticFeedback() {
        playDynamicHapticFeedback(forDistance: 2.0)
    }

    /// 播放强烈触觉反馈（兼容性方法）
    func playStrongHapticFeedback() {
        playDynamicHapticFeedback(forDistance: 0.5)
    }

    /// 🚀 播放超精细的基于距离的动态触觉反馈
    /// 使用最先进的CoreHaptics API，提供令人惊艳的触觉体验
    func playDynamicHapticFeedback(forDistance distance: Float) {
        hapticEventQueue.async { [weak self] in
            // 确保引擎处于可用状态
            self?.ensureEngineIsReady()
            self?.updateDistanceAndVelocity(distance)
            self?.startContinuousHapticFeedback()
        }
    }

    /// 开始持续的触觉反馈
    private func startContinuousHapticFeedback() {
        // 如果距离超出感知范围，停止反馈
        guard currentDistance > 0 && currentDistance <= 5.0 else {
            print("🛑 距离超出范围(\(currentDistance)米)，停止触觉反馈")
            stopHapticFeedback()
            return
        }

        shouldContinueHaptic = true

        // 停止现有的定时器
        continuousHapticTimer?.invalidate()

        // 立即播放一次
        playInstantHapticFeedback()

        // 根据距离计算精确的振动间隔
        let interval = calculatePreciseHapticInterval()

        // 启动持续振动定时器，并在每次触发时重新计算间隔
        DispatchQueue.main.async { [weak self] in
            self?.continuousHapticTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] timer in
                guard let self = self, self.shouldContinueHaptic else {
                    timer.invalidate()
                    return
                }

                // 重新计算间隔，如果距离变化了
                let newInterval = self.calculatePreciseHapticInterval()
                if abs(newInterval - timer.timeInterval) > 0.05 { // 如果间隔变化超过50ms
                    // 重新创建定时器
                    timer.invalidate()
                    self.startContinuousHapticFeedback()
                    return
                }

                self.hapticEventQueue.async {
                    self.playInstantHapticFeedback()
                }
            }
        }

        print("🎯 精确触觉反馈 - 距离: \(String(format: "%.2f", currentDistance))米, 间隔: \(String(format: "%.3f", interval))秒")
    }

    /// 计算精确的触觉反馈间隔（根据距离连续调整频率）
    private func calculatePreciseHapticInterval() -> TimeInterval {
        // 使用连续的数学函数来计算间隔，而不是分段的固定值
        // 距离越近，间隔越短（频率越高）

        let distance = max(0.1, min(currentDistance, 5.0)) // 限制在0.1-5.0米范围内

        // 使用指数函数：interval = 0.05 + 0.95 * (distance / 5.0)^2
        // 这样可以在近距离时提供非常高的频率，远距离时逐渐降低
        let normalizedDistance = distance / 5.0 // 归一化到0-1
        let exponentialFactor = pow(normalizedDistance, 1.5) // 使用1.5次方，提供平滑的过渡
        let interval = 0.05 + 1.45 * exponentialFactor // 最小50ms，最大1.5秒

        return TimeInterval(interval)
    }

    /// 旧的分段间隔计算（保留作为备用）
    private func calculateHapticInterval() -> TimeInterval {
        if currentDistance <= 0.5 {
            return 0.05 // 0.5米内：每50ms一次（超高频）
        } else if currentDistance <= 1.0 {
            return 0.1 // 0.5-1米：每100ms一次（高频）
        } else if currentDistance <= 2.0 {
            return 0.3 // 1-2米：每300ms一次（中频）
        } else if currentDistance <= 3.0 {
            return 0.6 // 2-3米：每600ms一次（低频）
        } else {
            return 1.2 // 3米以上：每1.2秒一次（很低频）
        }
    }

    /// 播放瞬时触觉反馈
    private func playInstantHapticFeedback() {
        do {
            try engine?.start()

            // 根据距离计算精确的强度和锐度
            let intensity = calculatePreciseIntensity()
            let sharpness = calculatePreciseSharpness()

            // 创建瞬时触觉事件
            let event = CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: intensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: sharpness)
                ],
                relativeTime: 0
            )

            let pattern = try CHHapticPattern(events: [event], parameters: [])
            let player = try engine?.makePlayer(with: pattern)
            try player?.start(atTime: CHHapticTimeImmediate)

            // 调试信息（降低频率）
            let now = CACurrentMediaTime()
            if now - lastUpdateTime > 2.0 { // 每2秒打印一次详细信息
                print("🎮 触觉参数 - 距离: \(String(format: "%.2f", currentDistance))米, 强度: \(String(format: "%.2f", intensity)), 锐度: \(String(format: "%.2f", sharpness))")
                lastUpdateTime = now
            }

        } catch {
            print("❌ 播放瞬时触觉反馈失败: \(error)")
        }
    }

    /// 计算精确的触觉强度（基于距离的连续函数）
    private func calculatePreciseIntensity() -> Float {
        let distance = max(0.1, min(currentDistance, 5.0)) // 限制在0.1-5.0米范围内
        let normalizedDistance = distance / 5.0 // 归一化到0-1

        // 使用反比例函数：intensity = 0.3 + 0.7 * (1 - distance/5)^1.8
        // 距离越近，强度越大，但不会过于极端
        let intensityFactor = pow(1.0 - normalizedDistance, 1.8)
        let intensity = 0.3 + 0.7 * intensityFactor // 最小0.3，最大1.0

        return Float(intensity)
    }

    /// 计算精确的触觉锐度（基于距离的连续函数）
    private func calculatePreciseSharpness() -> Float {
        let distance = max(0.1, min(currentDistance, 5.0)) // 限制在0.1-5.0米范围内
        let normalizedDistance = distance / 5.0 // 归一化到0-1

        // 使用反比例函数：sharpness = 0.2 + 0.6 * (1 - distance/5)^1.5
        // 距离越近，锐度越高，提供更清晰的触觉感受
        let sharpnessFactor = pow(1.0 - normalizedDistance, 1.5)
        let sharpness = 0.2 + 0.6 * sharpnessFactor // 最小0.2，最大0.8

        return Float(sharpness)
    }

    /// 确保触觉引擎处于就绪状态
    private func ensureEngineIsReady() {
        // 检查引擎是否存在且正在运行
        guard let engine = engine else {
            print("⚠️ 触觉引擎不存在，重新创建")
            setupAdvancedHapticEngine()
            return
        }

        // 检查引擎状态
        if engine.currentTime == 0 {
            print("⚠️ 触觉引擎未启动，尝试启动")
            do {
                try engine.start()
                print("✅ 触觉引擎重新启动成功")
            } catch {
                print("❌ 触觉引擎启动失败: \(error)")
                // 重新创建引擎
                setupAdvancedHapticEngine()
            }
        }
    }

    /// 更新距离和计算速度
    private func updateDistanceAndVelocity(_ newDistance: Float) {
        let currentTime = CACurrentMediaTime()

        if lastUpdateTime > 0 {
            let deltaTime = Float(currentTime - lastUpdateTime)
            if deltaTime > 0 {
                distanceVelocity = (newDistance - currentDistance) / deltaTime
            }
        }

        previousDistance = currentDistance
        currentDistance = newDistance
        lastUpdateTime = currentTime
    }

    /// 创建超精细触觉体验（已弃用，使用持续振动代替）
    private func createUltraPreciseHapticExperience() {
        // 重定向到新的持续振动系统
        startContinuousHapticFeedback()
    }

    /// 启动超精细模式
    private func startUltraPreciseMode() {
        do {
            try engine?.start()

            // 创建超精细触觉模式
            let pattern = createUltraPreciseHapticPattern()

            // 创建高级播放器
            advancedPlayer = try engine?.makeAdvancedPlayer(with: pattern)

            // 启动播放
            try advancedPlayer?.start(atTime: CHHapticTimeImmediate)

            currentMode = .ultraPrecise
            isPlaying = true

            print("🎯 超精细触觉模式已启动 - 距离: \(currentDistance)米")

        } catch {
            print("❌ 超精细触觉模式启动失败: \(error)")
        }
    }

    /// 🎨 创建超精细触觉模式
    private func createUltraPreciseHapticPattern() -> CHHapticPattern {
        var events: [CHHapticEvent] = []

        // 获取精度等级
        let precisionLevel = getPrecisionLevel(for: currentDistance)

        // 根据精度等级创建不同的触觉体验
        switch precisionLevel {
        case .ultraFine:
            events.append(contentsOf: createUltraFineHapticEvents())
        case .fine:
            events.append(contentsOf: createFineHapticEvents())
        case .standard:
            events.append(contentsOf: createStandardHapticEvents())
        case .coarse:
            events.append(contentsOf: createCoarseHapticEvents())
        }

        do {
            // 创建模式时不使用参数，而是通过动态参数控制
            return try CHHapticPattern(events: events, parameters: [])
        } catch {
            print("❌ 创建超精细触觉模式失败: \(error)")
            // 返回简单的备用模式
            return createFallbackPattern()
        }
    }

    /// 获取距离对应的精度等级
    private func getPrecisionLevel(for distance: Float) -> PrecisionLevel {
        switch distance {
        case 0...0.5:
            return .ultraFine
        case 0.5...1.5:
            return .fine
        case 1.5...3.0:
            return .standard
        default:
            return .coarse
        }
    }

    /// 🔥 创建超精细触觉事件（0-0.5米）- 最高精度
    private func createUltraFineHapticEvents() -> [CHHapticEvent] {
        var events: [CHHapticEvent] = []

        // 计算基础参数
        let baseIntensity = calculateUltraFineIntensity()
        let baseSharpness = calculateUltraFineSharpness()

        // 创建"心跳"式的复杂触觉体验
        for i in 0..<20 {
            let time = Double(i) * 0.025 // 每25ms一个脉冲，极高频率

            // 使用正弦波调制强度，创造波动感
            let intensityModulation = 0.8 + 0.2 * sin(Double(i) * 0.8)
            let sharpnessModulation = 0.9 + 0.1 * cos(Double(i) * 0.6)

            let intensity = baseIntensity * Float(intensityModulation)
            let sharpness = baseSharpness * Float(sharpnessModulation)

            // 主脉冲 - 尖锐的瞬时触觉
            let mainEvent = CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: intensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: sharpness)
                ],
                relativeTime: time
            )
            events.append(mainEvent)

            // 微妙的连续震动 - 增加层次感
            if i % 3 == 0 {
                let continuousEvent = CHHapticEvent(
                    eventType: .hapticContinuous,
                    parameters: [
                        CHHapticEventParameter(parameterID: .hapticIntensity, value: intensity * 0.4),
                        CHHapticEventParameter(parameterID: .hapticSharpness, value: sharpness * 0.6)
                    ],
                    relativeTime: time,
                    duration: 0.02
                )
                events.append(continuousEvent)
            }
        }

        return events
    }

    /// ⚡ 创建精细触觉事件（0.5-1.5米）
    private func createFineHapticEvents() -> [CHHapticEvent] {
        var events: [CHHapticEvent] = []

        let baseIntensity = calculateFineIntensity()
        let baseSharpness = calculateFineSharpness()

        // 创建节奏感强的触觉体验
        for i in 0..<12 {
            let time = Double(i) * 0.04 // 每40ms一个脉冲

            let intensity = baseIntensity * Float(0.7 + 0.3 * sin(Double(i) * 0.5))
            let sharpness = baseSharpness * Float(0.8 + 0.2 * cos(Double(i) * 0.4))

            let event = CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: intensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: sharpness)
                ],
                relativeTime: time
            )
            events.append(event)
        }

        return events
    }

    /// 📊 创建标准触觉事件（1.5-3.0米）
    private func createStandardHapticEvents() -> [CHHapticEvent] {
        var events: [CHHapticEvent] = []

        let baseIntensity = calculateStandardIntensity()
        let baseSharpness = calculateStandardSharpness()

        // 创建稳定的触觉体验
        for i in 0..<8 {
            let time = Double(i) * 0.06 // 每60ms一个脉冲

            let intensity = baseIntensity * Float(0.8 + 0.2 * sin(Double(i) * 0.3))
            let sharpness = baseSharpness

            let event = CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: intensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: sharpness)
                ],
                relativeTime: time
            )
            events.append(event)
        }

        return events
    }

    /// 🌊 创建粗糙触觉事件（3.0-5.0米）
    private func createCoarseHapticEvents() -> [CHHapticEvent] {
        var events: [CHHapticEvent] = []

        let baseIntensity = calculateCoarseIntensity()
        let baseSharpness = calculateCoarseSharpness()

        // 创建简单的触觉体验
        for i in 0..<5 {
            let time = Double(i) * 0.1 // 每100ms一个脉冲

            let event = CHHapticEvent(
                eventType: .hapticTransient,
                parameters: [
                    CHHapticEventParameter(parameterID: .hapticIntensity, value: baseIntensity),
                    CHHapticEventParameter(parameterID: .hapticSharpness, value: baseSharpness)
                ],
                relativeTime: time
            )
            events.append(event)
        }

        return events
    }

    /// 停止触觉反馈
    func stopHapticFeedback() {
        shouldContinueHaptic = false

        // 停止持续振动定时器
        DispatchQueue.main.async { [weak self] in
            self?.continuousHapticTimer?.invalidate()
            self?.continuousHapticTimer = nil
        }

        hapticEventQueue.async { [weak self] in
            do {
                try self?.advancedPlayer?.stop(atTime: CHHapticTimeImmediate)
            } catch {
                print("❌ 停止触觉反馈失败: \(error)")
            }
            self?.advancedPlayer = nil
            self?.currentMode = .none
            self?.isPlaying = false
            print("🛑 触觉反馈已完全停止")
        }
    }

    /// 🔧 更新动态参数
    private func updateDynamicParameters() {
        guard let player = advancedPlayer else { return }

        hapticEventQueue.async { [weak self] in
            guard let self = self else { return }

            do {
                // 计算新的强度和锐度
                let newIntensity = self.calculateDynamicIntensity()
                let newSharpness = self.calculateDynamicSharpness()

                // 更新播放器参数
                try player.sendParameters([
                    CHHapticDynamicParameter(parameterID: .hapticIntensityControl, value: newIntensity, relativeTime: 0),
                    CHHapticDynamicParameter(parameterID: .hapticSharpnessControl, value: newSharpness, relativeTime: 0)
                ], atTime: CHHapticTimeImmediate)

                print("🔄 动态参数已更新 - 距离: \(self.currentDistance)米, 强度: \(newIntensity), 锐度: \(newSharpness)")

            } catch {
                print("❌ 更新动态参数失败: \(error)")
            }
        }
    }

    /// 🧮 计算动态强度
    private func calculateDynamicIntensity() -> Float {
        // 使用复杂的非线性算法，考虑距离和速度
        let distanceComponent = pow(max(0.1, 5.0 - currentDistance) / 5.0, 1.8)
        let velocityComponent = min(abs(distanceVelocity) * 0.2, 0.3) // 速度影响

        return min(0.3 + distanceComponent * 0.7 + velocityComponent, 1.0)
    }

    /// 🧮 计算动态锐度
    private func calculateDynamicSharpness() -> Float {
        let distanceComponent = pow(max(0.1, 5.0 - currentDistance) / 5.0, 1.5)
        let velocityComponent = min(abs(distanceVelocity) * 0.15, 0.25)

        return min(0.2 + distanceComponent * 0.6 + velocityComponent, 1.0)
    }

    /// 🎯 计算超精细强度
    private func calculateUltraFineIntensity() -> Float {
        let normalizedDistance = max(0, min(currentDistance, 0.5)) / 0.5
        return 0.8 + (1.0 - normalizedDistance) * 0.2 // 0.8-1.0
    }

    /// 🎯 计算超精细锐度
    private func calculateUltraFineSharpness() -> Float {
        let normalizedDistance = max(0, min(currentDistance, 0.5)) / 0.5
        return 0.7 + (1.0 - normalizedDistance) * 0.3 // 0.7-1.0
    }

    /// ⚡ 计算精细强度
    private func calculateFineIntensity() -> Float {
        let normalizedDistance = max(0, min(currentDistance - 0.5, 1.0)) / 1.0
        return 0.6 + (1.0 - normalizedDistance) * 0.2 // 0.6-0.8
    }

    /// ⚡ 计算精细锐度
    private func calculateFineSharpness() -> Float {
        let normalizedDistance = max(0, min(currentDistance - 0.5, 1.0)) / 1.0
        return 0.5 + (1.0 - normalizedDistance) * 0.2 // 0.5-0.7
    }

    /// 📊 计算标准强度
    private func calculateStandardIntensity() -> Float {
        let normalizedDistance = max(0, min(currentDistance - 1.5, 1.5)) / 1.5
        return 0.4 + (1.0 - normalizedDistance) * 0.2 // 0.4-0.6
    }

    /// 📊 计算标准锐度
    private func calculateStandardSharpness() -> Float {
        let normalizedDistance = max(0, min(currentDistance - 1.5, 1.5)) / 1.5
        return 0.3 + (1.0 - normalizedDistance) * 0.2 // 0.3-0.5
    }

    /// 🌊 计算粗糙强度
    private func calculateCoarseIntensity() -> Float {
        let normalizedDistance = max(0, min(currentDistance - 3.0, 2.0)) / 2.0
        return 0.3 + (1.0 - normalizedDistance) * 0.1 // 0.3-0.4
    }

    /// 🌊 计算粗糙锐度
    private func calculateCoarseSharpness() -> Float {
        return 0.3 // 固定值
    }



    /// 🛡️ 创建备用模式
    private func createFallbackPattern() -> CHHapticPattern {
        let event = CHHapticEvent(
            eventType: .hapticTransient,
            parameters: [
                CHHapticEventParameter(parameterID: .hapticIntensity, value: 0.5),
                CHHapticEventParameter(parameterID: .hapticSharpness, value: 0.5)
            ],
            relativeTime: 0
        )

        do {
            return try CHHapticPattern(events: [event], parameters: [])
        } catch {
            fatalError("无法创建备用触觉模式: \(error)")
        }
    }

    /// 🔄 更新距离值（兼容性方法）
    func updateDistance(_ distance: Float) {
        // 直接调用动态触觉反馈方法
        playDynamicHapticFeedback(forDistance: distance)
    }

    /// 🎮 播放简单的触觉反馈（用于按钮点击等）
    func playSimpleHapticFeedback() {
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.prepare()
        generator.impactOccurred()
        print("🎮 简单触觉反馈已触发")
    }

    /// 🧪 测试触觉反馈功能
    func testHapticFeedback() {
        print("🧪 开始测试触觉反馈功能")

        // 检查设备支持
        let capabilities = CHHapticEngine.capabilitiesForHardware()
        print("  - 设备支持触觉: \(capabilities.supportsHaptics)")
        print("  - 设备支持音频: \(capabilities.supportsAudio)")

        // 检查引擎状态
        if let engine = engine {
            print("  - 引擎存在: ✅")
            print("  - 引擎当前时间: \(engine.currentTime)")
        } else {
            print("  - 引擎存在: ❌")
        }

        // 测试简单反馈
        print("🧪 测试简单触觉反馈...")
        playSimpleHapticFeedback()

        // 测试精确的距离感应触觉反馈
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            print("🧪 测试精确距离感应触觉反馈...")

            // 模拟从远到近的距离变化
            let distances: [Float] = [5.0, 3.0, 2.0, 1.0, 0.5, 0.2]
            var index = 0

            func testNextDistance() {
                guard index < distances.count else {
                    print("🧪 停止测试触觉反馈")
                    self.stopHapticFeedback()
                    print("🧪 精确触觉反馈测试完成")
                    return
                }

                let distance = distances[index]
                print("🧪 测试距离: \(distance)米")
                self.playDynamicHapticFeedback(forDistance: distance)

                index += 1
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    testNextDistance()
                }
            }

            testNextDistance()
        }
    }
}
